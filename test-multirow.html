<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>多行拖拽测试</title>
    <link rel="stylesheet" href="dist/frappe-gantt.css">
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
        }
        .gantt-container {
            margin: 20px 0;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
        .instructions {
            background: #f5f5f5;
            padding: 15px;
            border-radius: 4px;
            margin-bottom: 20px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>甘特图多行拖拽测试</h1>
        
        <div class="instructions">
            <h3>测试说明：</h3>
            <ul>
                <li>尝试拖拽任务条到不同的行</li>
                <li>观察任务是否能在同一行共存</li>
                <li>检查依赖箭头是否正确更新</li>
                <li>验证现有的水平拖拽功能是否正常</li>
            </ul>
        </div>

        <div class="gantt-container" id="gantt"></div>
    </div>

    <script src="dist/frappe-gantt.umd.js"></script>
    <script>
        // 创建测试数据
        const today = new Date();
        const daysSince = (dx) => new Date(today.getTime() + dx * 86400000);

        let tasks = [
            {
                start: daysSince(-2),
                end: daysSince(2),
                name: '任务 A',
                id: 'Task_A',
                progress: 30
            },
            {
                start: daysSince(3),
                end: daysSince(7),
                name: '任务 B',
                id: 'Task_B',
                progress: 60,
                dependencies: 'Task_A'
            },
            {
                start: daysSince(1),
                end: daysSince(4),
                name: '任务 C',
                id: 'Task_C',
                progress: 80
            },
            {
                start: daysSince(5),
                end: daysSince(8),
                name: '任务 D',
                id: 'Task_D',
                progress: 20
            },
            {
                start: daysSince(-1),
                end: daysSince(1),
                name: '任务 E',
                id: 'Task_E',
                progress: 90
            }
        ];

        // 添加调试信息
        console.log('初始任务数据:', tasks);

        try {
            // 创建甘特图
            const gantt = new Gantt('#gantt', tasks, {
                header_height: 50,
                column_width: 30,
                step: 24,
                view_modes: ['Quarter Day', 'Half Day', 'Day', 'Week', 'Month'],
                bar_height: 20,
                bar_corner_radius: 3,
                arrow_curve: 5,
                padding: 18,
                view_mode: 'Day',
                date_format: 'YYYY-MM-DD',
                popup_trigger: 'click',
                on_click: function (task) {
                    console.log('点击任务:', task);
                },
                on_date_change: function (task, start, end) {
                    console.log('任务日期变更:', task.name, start, end);
                },
                on_progress_change: function (task, progress) {
                    console.log('任务进度变更:', task.name, progress);
                },
                on_view_change: function (mode) {
                    console.log('视图模式变更:', mode);
                }
            });

            console.log('甘特图创建成功');
            console.log('任务布局:', gantt.tasks.map(t => ({name: t.name, row: t._row, position: t._row_position})));
        } catch (error) {
            console.error('甘特图创建失败:', error);
        }

        // 添加视图切换按钮
        const viewModes = ['Quarter Day', 'Half Day', 'Day', 'Week', 'Month'];
        const buttonContainer = document.createElement('div');
        buttonContainer.style.marginBottom = '10px';
        
        viewModes.forEach(mode => {
            const button = document.createElement('button');
            button.textContent = mode;
            button.style.marginRight = '10px';
            button.style.padding = '5px 10px';
            button.onclick = () => gantt.change_view_mode(mode);
            buttonContainer.appendChild(button);
        });
        
        document.querySelector('.gantt-container').parentNode.insertBefore(buttonContainer, document.querySelector('.gantt-container'));
    </script>
</body>
</html>
