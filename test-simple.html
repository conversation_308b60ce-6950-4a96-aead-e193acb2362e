<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>简单测试</title>
    <link rel="stylesheet" href="dist/frappe-gantt.css">
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
        }
        .gantt-container {
            margin: 20px 0;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>甘特图基本功能测试</h1>
        <div class="gantt-container" id="gantt"></div>
    </div>

    <script src="dist/frappe-gantt.umd.js"></script>
    <script>
        // 创建简单的测试数据
        const today = new Date();
        const daysSince = (dx) => new Date(today.getTime() + dx * 86400000);

        let tasks = [
            {
                start: daysSince(-2),
                end: daysSince(2),
                name: '任务 1',
                id: 'Task_1',
                progress: 30
            },
            {
                start: daysSince(3),
                end: daysSince(7),
                name: '任务 2',
                id: 'Task_2',
                progress: 60
            },
            {
                start: daysSince(1),
                end: daysSince(4),
                name: '任务 3',
                id: 'Task_3',
                progress: 80
            }
        ];

        console.log('任务数据:', tasks);

        try {
            // 创建甘特图
            const gantt = new Gantt('#gantt', tasks, {
                header_height: 50,
                column_width: 30,
                step: 24,
                bar_height: 20,
                bar_corner_radius: 3,
                arrow_curve: 5,
                padding: 18,
                view_mode: 'Day',
                date_format: 'YYYY-MM-DD'
            });
            
            console.log('甘特图创建成功');
        } catch (error) {
            console.error('甘特图创建失败:', error);
        }
    </script>
</body>
</html>
