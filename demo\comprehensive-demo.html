<!doctype html>
<html lang="zh-cn">
<head>
    <meta charset="UTF-8" />
    <title>Frappe Gantt 综合案例演示</title>
    <link rel="stylesheet" href="../dist/frappe-gantt.css" />
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet" />
    <style>
        .container { width: 92%; margin: 0 auto; }
        .chart { border: 1px solid #bbb; border-radius: 4px; margin-top: 32px; }
        .bar-start .bar { fill: #4f8cff !important; }
        .bar-analysis .bar { fill: #34c759 !important; }
        .bar-design .bar { fill: #ff9500 !important; }
        .bar-dev .bar { fill: #ff3b30 !important; }
        .bar-test .bar { fill: #a259ff !important; }
        .bar-release .bar { fill: #ffd60a !important; }
        /* 进度条自定义颜色 */
        .bar-start .bar-progress { fill: #1e40af !important; }
        .bar-analysis .bar-progress { fill: #15803d !important; }
        .bar-design .bar-progress { fill: #b45309 !important; }
        .bar-dev .bar-progress { fill: #b91c1c !important; }
        .bar-test .bar-progress { fill: #7c3aed !important; }
        .bar-release .bar-progress { fill: #eab308 !important; }
    </style>
    <script src="../dist/frappe-gantt.umd.js"></script>
</head>
<body>
<div class="container">
    <h1 class="text-center pt-4 pb-2">Frappe Gantt 综合案例演示</h1>
    <p class="text-center">本案例集成了视图切换、任务/进度自定义颜色、依赖连线、假期高亮、周末高亮、today按钮、自动滚动、label自动移动等全部主要功能。</p>
    <div class="row">
        <div class="col-12">
            <div class="chart" id="comprehensive-demo"></div>
        </div>
    </div>
    <div class="row mt-4">
        <div class="col-md-6">
            <label for="view-mode-select" class="form-label">视图模式：</label>
            <select id="view-mode-select" class="form-select w-auto d-inline-block">
                <option value="Day">天</option>
                <option value="Week">周</option>
                <option value="Month">月</option>
                <option value="Year">年</option>
            </select>
            <button id="today-btn" class="btn btn-outline-primary ms-3">滚动到今天</button>
        </div>
        <div class="col-md-6">
            <label class="form-label me-2">Snap By:</label>
            <input class="form-control d-inline-block w-auto" id="snap-at-qty" type="number" value="1" style="width:60px;" />
            <select class="form-select d-inline-block w-auto" id="snap-at-scale">
                <option value="s">秒</option>
                <option value="min">分钟</option>
                <option value="h">小时</option>
                <option value="d" selected>天</option>
                <option value="m">月</option>
                <option value="y">年</option>
            </select>
            <div class="form-check form-switch d-inline-block ms-3">
                <input class="form-check-input" type="checkbox" id="auto-move-label" checked />
                <label class="form-check-label" for="auto-move-label">自动移动标签</label>
            </div>
        </div>
    </div>
</div>
<script type="module">
    const rawToday = new Date();
    const today = Date.UTC(rawToday.getFullYear(), rawToday.getMonth(), rawToday.getDate()) + new Date().getTimezoneOffset() * 60000;
    const daysSince = (dx) => new Date(today + dx * 86400000);
    function random(begin = 10, end = 90, multiple = 10) {
        let k;
        do { k = Math.floor(Math.random() * 100); } while (k < begin || k > end || k % multiple !== 0);
        return k;
    }
    // 综合案例任务数据
    const comprehensiveTasks = [
        { start: daysSince(-5), end: daysSince(2), name: '项目启动', id: 'T1', progress: 100, custom_class: 'bar-start', color: '#4f8cff' },
        { start: daysSince(2), end: daysSince(7), name: '需求分析', id: 'T2', progress: 60, dependencies: 'T1', custom_class: 'bar-analysis', color: '#34c759' },
        { start: daysSince(7), end: daysSince(14), name: '设计', id: 'T3', progress: 30, dependencies: 'T2', custom_class: 'bar-design', color: '#ff9500' },
        { start: daysSince(14), end: daysSince(22), name: '开发', id: 'T4', progress: 0, dependencies: 'T3', custom_class: 'bar-dev', color: '#ff3b30' },
        { start: daysSince(22), end: daysSince(28), name: '测试', id: 'T5', progress: 0, dependencies: 'T4', custom_class: 'bar-test', color: '#a259ff' },
        { start: daysSince(28), end: daysSince(32), name: '上线', id: 'T6', progress: 0, dependencies: 'T5', custom_class: 'bar-release', color: '#ffd60a' },
    ];
    // 综合案例假期
    const comprehensiveHolidays = {
        '#e0e7ff': [
            { name: '端午节', date: '2025-06-01' },
            { name: '国庆节', date: '2025-10-01' },
        ],
        '#ffe4e6': 'weekend',
    };
    // Gantt 初始化
    let comprehensiveGantt = new Gantt('#comprehensive-demo', comprehensiveTasks, {
        view_mode: 'Day',
        view_mode_select: true,
        today_button: true,
        holidays: comprehensiveHolidays,
        ignore: [],
        bar_height: 32,
        bar_corner_radius: 8,
        arrow_curve: 8,
        column_width: 44,
        container_height: 340,
        padding: 22,
        custom_popup_html: null,
        scroll_to: 'today',
        snap_at: '1d',
        auto_move_label: true,
    });
    // 视图模式切换
    document.getElementById('view-mode-select').onchange = function(e) {
        comprehensiveGantt.change_view_mode(e.target.value);
    };
    // 滚动到今天
    document.getElementById('today-btn').onclick = function() {
        comprehensiveGantt.update_options({ scroll_to: 'today' });
    };
    // Snap By
    function updateSnap() {
        const qty = document.getElementById('snap-at-qty').value;
        const scale = document.getElementById('snap-at-scale').value;
        comprehensiveGantt.update_options({ snap_at: qty + scale });
    }
    document.getElementById('snap-at-qty').onchange = updateSnap;
    document.getElementById('snap-at-scale').onchange = updateSnap;
    // 自动移动标签
    document.getElementById('auto-move-label').onchange = function(e) {
        comprehensiveGantt.update_options({ auto_move_label: e.target.checked });
    };
</script>
<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
